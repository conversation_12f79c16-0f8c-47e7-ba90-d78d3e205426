# 故障排除指南

## 常见问题及解决方案

### 1. 一直显示"正在努力从多个源抓取代理... 请稍候"

**问题原因：**
- CORS（跨域资源共享）限制
- 网络连接问题
- 代理数据源服务器不可用

**解决方案：**

#### 方案一：使用演示数据（推荐）
1. 点击页面上的"加载演示数据"按钮
2. 这将加载预设的测试代理数据
3. 可以正常体验所有功能

#### 方案二：检查网络连接
1. 确保网络连接正常
2. 尝试访问其他网站验证网络
3. 刷新页面重试

#### 方案三：使用本地服务器
```bash
# 启动本地HTTP服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后访问 http://localhost:8000
```

#### 方案四：禁用浏览器CORS检查（仅用于测试）
```bash
# Chrome（Windows）
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security

# Chrome（Mac）
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security

# Firefox
# 在地址栏输入 about:config
# 搜索 security.tls.insecure_fallback_hosts
# 添加需要的域名
```

### 2. 代理测试总是失败

**问题原因：**
- 免费代理本身质量较低
- 网络环境限制
- 代理服务器不稳定

**解决方案：**
1. **正常现象**：免费代理成功率通常只有20-40%
2. **多次测试**：可以多测试几个代理
3. **选择不同类型**：尝试HTTP、SOCKS4、SOCKS5不同类型
4. **检查端口**：常见端口（80、8080、3128）成功率更高

### 3. 无法导出数据

**问题原因：**
- 浏览器阻止下载
- 没有选择代理数据
- JavaScript权限问题

**解决方案：**
1. **检查浏览器设置**：
   - 允许下载文件
   - 检查下载文件夹权限
2. **确保有数据**：
   - 先获取或加载代理数据
   - 确认表格中有代理显示
3. **尝试不同格式**：
   - TXT格式最简单
   - CSV格式包含完整信息
   - JSON格式适合程序处理

### 4. 页面加载缓慢

**问题原因：**
- 代理数量过多
- 网络请求过多
- 浏览器性能限制

**解决方案：**
1. **使用筛选功能**：
   - 按国家筛选减少数据量
   - 使用搜索功能快速定位
2. **分页浏览**：
   - 每页显示50个代理
   - 避免一次加载过多数据
3. **关闭其他标签页**：
   - 释放浏览器内存
   - 提高页面响应速度

### 5. 深色模式不工作

**问题原因：**
- 浏览器不支持
- CSS加载问题
- 本地存储问题

**解决方案：**
1. **手动切换**：点击右上角的主题切换按钮
2. **清除缓存**：清除浏览器缓存和本地存储
3. **更新浏览器**：使用最新版本的现代浏览器

### 6. 统计数据不更新

**问题原因：**
- JavaScript执行错误
- 数据同步问题
- 浏览器兼容性

**解决方案：**
1. **刷新页面**：F5或Ctrl+R刷新
2. **检查控制台**：F12打开开发者工具查看错误
3. **重新获取数据**：点击"获取/刷新代理"按钮

## 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### 不支持的浏览器
- ❌ Internet Explorer
- ❌ 旧版本移动浏览器

## 网络要求

### 最低要求
- 稳定的互联网连接
- 支持HTTPS的网络环境
- 允许跨域请求（或使用本地服务器）

### 推荐配置
- 宽带网络连接
- 现代浏览器
- 允许JavaScript执行

## 性能优化建议

### 1. 减少数据量
```javascript
// 使用筛选功能
- 按国家筛选：US, JP, CN
- 按类型筛选：HTTP, SOCKS4, SOCKS5
- 使用搜索：输入关键词快速定位
```

### 2. 分批处理
```javascript
// 避免同时测试过多代理
- 使用"测试当前页代理"而不是全部测试
- 选择部分代理进行测试
- 分页浏览大量数据
```

### 3. 定期清理
```javascript
// 定期刷新数据
- 获取最新的代理列表
- 清除失效的代理
- 重置测试结果
```

## 开发者调试

### 1. 打开开发者工具
```
F12 或 Ctrl+Shift+I
```

### 2. 查看控制台日志
```javascript
// 查看网络请求
Network 标签页

// 查看JavaScript错误
Console 标签页

// 查看元素结构
Elements 标签页
```

### 3. 常见错误信息
```
CORS error: 跨域请求被阻止
Network error: 网络连接问题
Timeout error: 请求超时
Parse error: 数据解析失败
```

## 联系支持

如果以上解决方案都无法解决问题：

1. **检查浏览器控制台**：记录错误信息
2. **尝试不同浏览器**：排除浏览器特定问题
3. **使用演示数据**：验证功能是否正常
4. **查看项目文档**：README.md 和 USAGE.md

## 常用快捷操作

### 快速开始
1. 打开页面
2. 点击"加载演示数据"
3. 开始使用所有功能

### 快速测试
1. 选择代理类型
2. 点击"获取/刷新代理"
3. 点击"测试当前页代理"
4. 查看测试结果

### 快速导出
1. 确保有代理数据
2. 选择需要的代理（可选）
3. 点击导出按钮
4. 选择格式下载

## 更新日志

### v2.1.0 (当前版本)
- ✅ 修复CORS问题
- ✅ 添加演示数据功能
- ✅ 改进错误处理
- ✅ 添加网络状态检测
- ✅ 优化用户体验

### v2.0.0
- ✅ 多数据源支持
- ✅ 代理选择功能
- ✅ 统计面板
- ✅ 多格式导出
