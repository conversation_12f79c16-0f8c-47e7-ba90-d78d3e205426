<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理聚合平台 - 使用演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .demo-step { transition: all 0.3s ease; }
        .demo-step:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">🌐 免费代理聚合平台</h1>
            <p class="text-xl text-gray-600 mb-6">一站式代理获取、测试、管理解决方案</p>
            <div class="flex justify-center gap-4">
                <a href="index.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 shadow-lg">
                    🚀 立即使用
                </a>
                <a href="#features" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition duration-300 shadow-lg border border-blue-200">
                    📖 了解更多
                </a>
            </div>
        </header>

        <!-- Features Overview -->
        <section id="features" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">✨ 核心功能</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">📡</div>
                    <h3 class="text-xl font-semibold mb-2">多源聚合</h3>
                    <p class="text-gray-600">从11个不同数据源获取代理，包括ProxyScrape、Geonode等知名服务</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">🔍</div>
                    <h3 class="text-xl font-semibold mb-2">智能筛选</h3>
                    <p class="text-gray-600">支持按类型、国家、状态筛选，全局搜索快速定位目标代理</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">🧪</div>
                    <h3 class="text-xl font-semibold mb-2">实时测试</h3>
                    <p class="text-gray-600">单个或批量测试代理可用性，显示延迟和状态信息</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">📊</div>
                    <h3 class="text-xl font-semibold mb-2">数据统计</h3>
                    <p class="text-gray-600">实时显示代理统计信息和数据源状态监控</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">💾</div>
                    <h3 class="text-xl font-semibold mb-2">多格式导出</h3>
                    <p class="text-gray-600">支持TXT、CSV、JSON格式导出，满足不同使用需求</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg demo-step">
                    <div class="text-3xl mb-4">🎨</div>
                    <h3 class="text-xl font-semibold mb-2">优雅界面</h3>
                    <p class="text-gray-600">响应式设计，支持深色模式，提供流畅的用户体验</p>
                </div>
            </div>
        </section>

        <!-- Usage Steps -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">🚀 使用步骤</h2>
            <div class="max-w-4xl mx-auto">
                <div class="space-y-6">
                    <div class="flex items-start space-x-4 bg-white p-6 rounded-xl shadow-lg demo-step">
                        <div class="bg-blue-100 text-blue-600 w-8 h-8 rounded-full flex items-center justify-center font-bold">1</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">选择代理类型</h3>
                            <p class="text-gray-600">在下拉菜单中选择需要的代理类型：HTTP/HTTPS、SOCKS4或SOCKS5</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 bg-white p-6 rounded-xl shadow-lg demo-step">
                        <div class="bg-blue-100 text-blue-600 w-8 h-8 rounded-full flex items-center justify-center font-bold">2</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">获取代理列表</h3>
                            <p class="text-gray-600">点击"获取/刷新代理"按钮，系统将从多个数据源并发获取最新代理</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 bg-white p-6 rounded-xl shadow-lg demo-step">
                        <div class="bg-blue-100 text-blue-600 w-8 h-8 rounded-full flex items-center justify-center font-bold">3</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">筛选和搜索</h3>
                            <p class="text-gray-600">使用国家筛选和全局搜索功能快速找到符合需求的代理</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 bg-white p-6 rounded-xl shadow-lg demo-step">
                        <div class="bg-blue-100 text-blue-600 w-8 h-8 rounded-full flex items-center justify-center font-bold">4</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">测试代理</h3>
                            <p class="text-gray-600">选择需要测试的代理，点击测试按钮验证可用性和延迟</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 bg-white p-6 rounded-xl shadow-lg demo-step">
                        <div class="bg-blue-100 text-blue-600 w-8 h-8 rounded-full flex items-center justify-center font-bold">5</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">导出使用</h3>
                            <p class="text-gray-600">选择合适的格式导出代理列表，在您的应用中使用</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Data Sources -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">📡 数据源</h2>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="text-center p-4 border rounded-lg">
                        <h4 class="font-semibold text-lg mb-2">ProxyScrape</h4>
                        <p class="text-sm text-gray-600">专业代理API服务</p>
                        <div class="mt-2">
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">实时更新</span>
                        </div>
                    </div>
                    <div class="text-center p-4 border rounded-lg">
                        <h4 class="font-semibold text-lg mb-2">Geonode</h4>
                        <p class="text-sm text-gray-600">高质量代理列表</p>
                        <div class="mt-2">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">每小时更新</span>
                        </div>
                    </div>
                    <div class="text-center p-4 border rounded-lg">
                        <h4 class="font-semibold text-lg mb-2">GitHub开源</h4>
                        <p class="text-sm text-gray-600">多个开源项目</p>
                        <div class="mt-2">
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">每日更新</span>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-6">
                    <p class="text-gray-600">总计 <strong>11个</strong> 可靠数据源，确保代理列表的丰富性和时效性</p>
                </div>
            </div>
        </section>

        <!-- Security Warning -->
        <section class="mb-16">
            <div class="bg-red-50 border-l-4 border-red-500 p-6 rounded-lg">
                <div class="flex items-start">
                    <div class="text-red-500 text-2xl mr-4">⚠️</div>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800 mb-2">安全提示</h3>
                        <p class="text-red-700 mb-2">免费代理存在安全风险，请注意：</p>
                        <ul class="list-disc list-inside text-red-700 space-y-1">
                            <li>不要通过免费代理处理敏感信息</li>
                            <li>仅用于学习和测试目的</li>
                            <li>定期更换代理以降低风险</li>
                            <li>重要业务建议使用付费VPN服务</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center text-gray-600">
            <div class="border-t pt-8">
                <p>&copy; 2025 免费代理聚合平台. 仅供学习和研究使用.</p>
                <div class="mt-4 space-x-4">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800">使用工具</a>
                    <a href="README.md" class="text-blue-600 hover:text-blue-800">文档</a>
                    <a href="https://github.com" class="text-blue-600 hover:text-blue-800">GitHub</a>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 简单的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.demo-step');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            steps.forEach(step => {
                step.style.opacity = '0';
                step.style.transform = 'translateY(20px)';
                observer.observe(step);
            });
        });
    </script>
</body>
</html>
