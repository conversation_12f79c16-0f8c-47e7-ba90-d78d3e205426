<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全功能免费代理聚合平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        // On page load or when changing themes, best to add inline in `head` to avoid FOUC
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
    </script>
    <style>
        body {
            font-family: 'Inter', 'Helvetica Neue', 'Arial', 'sans-serif';
        }
        .loader {
            border-top-color: #3498db;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        th {
            cursor: pointer;
            user-select: none;
        }
        th .sort-icon {
            display: inline-block;
            width: 1em;
            text-align: center;
            opacity: 0.4;
        }
        .status-dot {
            height: 10px;
            width: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        .status-testing {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.4; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-300">

    <div class="container mx-auto p-4 md:p-8">
        <header class="text-center mb-8 relative">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">全功能免费代理聚合平台</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">聚合、测试、筛选并导出最新的免费代理</p>
            <button id="theme-toggle" class="absolute top-0 right-0 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none">
                <!-- Sun Icon -->
                <svg id="theme-toggle-light-icon" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>
                <!-- Moon Icon -->
                <svg id="theme-toggle-dark-icon" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>
            </button>
        </header>

        <!-- 安全警告 -->
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-8 shadow-md dark:bg-red-900/20 dark:text-red-300 dark:border-red-600" role="alert">
            <p class="font-bold">重要安全提示</p>
            <p>免费代理来源不明，可能存在安全和隐私风险。请**不要**在通过免费代理上网时处理任何敏感信息。此工具仅供学习和测试目的使用。</p>
        </div>

        <!-- 控制区域 -->
        <div class="space-y-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <div>
                    <label for="proxy-type" class="font-semibold mr-2 block mb-1">代理类型:</label>
                    <select id="proxy-type" class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-full bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                        <option value="all">所有类型</option>
                        <option value="http" selected>HTTP/HTTPS</option>
                        <option value="socks4">SOCKS4</option>
                        <option value="socks5">SOCKS5</option>
                    </select>
                </div>
                 <div>
                    <label for="country-filter" class="font-semibold mr-2 block mb-1">筛选国家 (代码):</label>
                    <input type="text" id="country-filter" placeholder="例如: US, JP" class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-full bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                </div>
                <div>
                    <label for="search-input" class="font-semibold mr-2 block mb-1">全局搜索:</label>
                    <input type="text" id="search-input" placeholder="搜索IP, 端口, 国家..." class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-full bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                </div>
                <div class="self-end">
                    <button id="fetch-proxies" class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-500 transition duration-300 shadow-md flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7V9a1 1 0 01-2 0V4a1 1 0 011-1zm12 2.101V4a1 1 0 012 0v3a1 1 0 01-1 1h-3a1 1 0 01-.894-1.447l1.447-2.567a5.002 5.002 0 00-9.284 0l1.447 2.567A1 1 0 0112 11h3a1 1 0 011 1v3a1 1 0 01-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 111.885-.666A5.002 5.002 0 0014.001 13V11a1 1 0 012 0v2.101z" clip-rule="evenodd" />
                        </svg>
                        获取/刷新代理
                    </button>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row gap-3 bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                 <button id="test-all-visible" class="flex-1 bg-yellow-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-yellow-600 transition duration-300 shadow-md">测试当前页代理</button>
                 <button id="export-txt" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition duration-300 shadow-md">导出为 TXT</button>
                 <button id="export-csv" class="flex-1 bg-teal-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-teal-700 transition duration-300 shadow-md">导出为 CSV</button>
            </div>
        </div>

        <!-- 代理列表 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div id="loader" class="hidden text-center p-10">
                <div class="loader ease-linear rounded-full border-8 border-t-8 border-gray-200 dark:border-gray-600 h-24 w-24 mx-auto"></div>
                <p class="mt-4 text-gray-600 dark:text-gray-300 font-semibold">正在努力从多个源抓取代理... 请稍候。</p>
            </div>
            <div id="error-message" class="hidden text-center p-10 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-300">
                <p class="font-bold">糟糕，出错了！</p>
                <p>无法获取代理列表。可能是网络问题或源服务器暂时不可用。请稍后再试。</p>
            </div>
            <div class="overflow-x-auto">
                <table id="proxy-table" class="min-w-full leading-normal">
                    <thead class="bg-gray-100 dark:bg-gray-700">
                        <tr>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider" data-sort="status">状态 <span class="sort-icon"></span></th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider" data-sort="ip">IP 地址 <span class="sort-icon"></span></th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider" data-sort="port">端口 <span class="sort-icon"></span></th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider" data-sort="type">类型 <span class="sort-icon"></span></th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider" data-sort="country">国家/地区 <span class="sort-icon"></span></th>
                            <th class="px-5 py-3 border-b-2 border-gray-200 dark:border-gray-600 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="proxy-list" class="bg-white dark:bg-gray-800"></tbody>
                </table>
            </div>
             <div id="pagination-controls" class="px-5 py-5 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex flex-col xs:flex-row items-center xs:justify-between">
                <span id="proxy-count" class="text-xs text-gray-600 dark:text-gray-400"></span>
                <div class="inline-flex mt-2 xs:mt-0">
                    <button id="prev-page" class="text-sm bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-l dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">上一页</button>
                    <span id="page-info" class="text-sm bg-gray-200 text-gray-800 font-semibold py-2 px-4 dark:bg-gray-700 dark:text-white"></span>
                    <button id="next-page" class="text-sm bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-r dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">下一页</button>
                </div>
            </div>
        </div>
        
        <footer class="text-center mt-12 text-gray-500 dark:text-gray-400 text-sm">
            <p>&copy; 2025 全功能免费代理聚合平台。数据仅供参考。</p>
        </footer>
    </div>
    
    <div id="toast" class="fixed bottom-10 right-10 bg-green-500 text-white py-2 px-4 rounded-lg shadow-xl transition-opacity duration-300 opacity-0"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Element Refs
            const fetchButton = document.getElementById('fetch-proxies');
            const proxyList = document.getElementById('proxy-list');
            const loader = document.getElementById('loader');
            const errorMessage = document.getElementById('error-message');
            const proxyTable = document.getElementById('proxy-table');
            const proxyTypeSelect = document.getElementById('proxy-type');
            const countryFilterInput = document.getElementById('country-filter');
            const searchInput = document.getElementById('search-input');
            const proxyCountSpan = document.getElementById('proxy-count');
            const toast = document.getElementById('toast');
            const themeToggleBtn = document.getElementById('theme-toggle');
            const lightIcon = document.getElementById('theme-toggle-light-icon');
            const darkIcon = document.getElementById('theme-toggle-dark-icon');
            const testAllBtn = document.getElementById('test-all-visible');
            const exportTxtBtn = document.getElementById('export-txt');
            const exportCsvBtn = document.getElementById('export-csv');
            const prevPageBtn = document.getElementById('prev-page');
            const nextPageBtn = document.getElementById('next-page');
            const pageInfoSpan = document.getElementById('page-info');

            // State
            let allProxies = [];
            let filteredProxies = [];
            let currentSort = { column: 'status', direction: 'asc' };
            let currentPage = 1;
            const ROWS_PER_PAGE = 50;

            // --- PROXY SOURCES (数据源) ---
            const proxySources = {
                // Source 1: ProxyScrape
                proxyscrape_http: { 
                    url: 'https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=http&proxy_format=ipport&format=text',
                    type: 'http',
                    parser: parseIpPortFormat
                },
                proxyscrape_socks4: {
                     url: 'https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=socks4&proxy_format=ipport&format=text',
                     type: 'socks4',
                     parser: parseIpPortFormat
                },
                proxyscrape_socks5: {
                     url: 'https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=socks5&proxy_format=ipport&format=text',
                     type: 'socks5',
                     parser: parseIpPortFormat
                },
                // Source 2: Geonode
                geonode: {
                    url: 'https://proxylist.geonode.com/api/proxy-list?limit=500&sort_by=lastChecked&sort_type=desc',
                    type: 'all', 
                    parser: parseGeonodeJsonFormat
                }
            };
            
            function parseIpPortFormat(text, type) {
                if (typeof text !== 'string') return [];
                return text.trim().split('\n').map(line => {
                    const [ip, port] = line.trim().split(':');
                    if (ip && port && ip.includes('.')) { 
                        return { ip, port, type: type.toUpperCase(), id: `${ip}:${port}` };
                    }
                    return null;
                }).filter(p => p);
            }

            function parseGeonodeJsonFormat(text) {
                 try {
                    const actualData = JSON.parse(text); 
                    if (!actualData.data) return [];
                    return actualData.data.map(p => ({
                        ip: p.ip,
                        port: p.port,
                        type: p.protocols[0].toUpperCase(),
                        country: p.country,
                        countryCode: p.iso_3166_2,
                        id: `${p.ip}:${p.port}`
                    }));
                 } catch(e) {
                    console.error("Failed to parse Geonode JSON:", text);
                    return [];
                 }
            }

            // --- CORE FUNCTIONS ---
            async function fetchWithTimeout(resource, options = {}, timeout = 15000) {
                const controller = new AbortController();
                const id = setTimeout(() => {
                    controller.abort();
                }, timeout);
                
                // FIX: Using a more reliable CORS proxy service that is less likely to be blocked.
                const proxyUrl = `https://api.codetabs.com/v1/proxy?quest=${encodeURIComponent(resource)}`;

                const response = await fetch(proxyUrl, { ...options, signal: controller.signal });
                clearTimeout(id);
                return response;
            }

            async function fetchProxies() {
                loader.classList.remove('hidden');
                errorMessage.classList.add('hidden');
                proxyTable.classList.add('hidden');
                document.getElementById('pagination-controls').classList.add('hidden');
                proxyList.innerHTML = '';
                allProxies = [];
                currentPage = 1;

                const selectedType = proxyTypeSelect.value;
                const sourcesToFetch = Object.values(proxySources).filter(s => selectedType === 'all' || s.type === 'all' || s.type === selectedType);
                
                try {
                    const promises = sourcesToFetch.map(source => 
                        fetchWithTimeout(source.url)
                            .then(res => {
                                if (!res.ok) {
                                    throw new Error(`Failed to fetch from ${source.url} with status ${res.status}`);
                                }
                                // The new proxy passes content directly, so we get text and let the parser handle it
                                return res.text();
                            })
                            .then(text => {
                               return source.parser(text, source.type);
                            })
                    );
                    
                    const results = await Promise.allSettled(promises);
                    const uniqueProxies = new Map();

                    for (const result of results) {
                        if (result.status === 'fulfilled' && result.value) {
                            result.value.forEach(proxy => {
                                if (!uniqueProxies.has(proxy.id)) {
                                     uniqueProxies.set(proxy.id, { ...proxy, status: 'untested', latency: Infinity });
                                }
                            });
                        } else {
                            console.error(`Error fetching or parsing from a source:`, result.reason);
                        }
                    }
                    allProxies = Array.from(uniqueProxies.values());

                    if (allProxies.length === 0) {
                        throw new Error("No proxies fetched from any source. This might be due to network issues or CORS policies from the proxy providers.");
                    }
                    
                    await enrichWithCountryInfo();

                } catch (error) {
                    console.error('Main fetchProxies error:', error);
                    errorMessage.querySelector('p:last-child').textContent = error.message;
                    errorMessage.classList.remove('hidden');
                } finally {
                    loader.classList.add('hidden');
                }
            }
            
            async function getGeoInfo(ip) {
                const geoAPIs = [
                    { url: `https://ipapi.co/${ip}/json/` },
                    { url: `https://api.ipapi.is?q=${ip}` },
                    { url: `https://ipinfo.io/${ip}/json` }
                ];
                
                function parseGeoData(data, sourceUrl) {
                     if (sourceUrl.includes('ipapi.co')) {
                        return (data && data.country_code) ? { country: data.country_name || data.country_code, countryCode: data.country_code } : null;
                     }
                     if (sourceUrl.includes('ipapi.is')) {
                        return (data && data.country_code) ? { country: data.country, countryCode: data.country_code } : null;
                     }
                     if (sourceUrl.includes('ipinfo.io')) {
                        return (data && data.country) ? { country: data.country, countryCode: data.country } : null;
                     }
                     return null;
                }

                for (const api of geoAPIs) {
                    try {
                        const res = await fetchWithTimeout(api.url, {}, 3000);
                        if (!res.ok) continue;
                        const data = await res.json();
                        const result = parseGeoData(data, api.url);
                        if (result) return result;
                    } catch (e) {
                        continue;
                    }
                }
                return { country: '查询失败', countryCode: 'N/A' };
            }
            
            async function enrichWithCountryInfo() {
                const proxiesToEnrich = allProxies.filter(p => !p.country);
                
                const batchSize = 15;
                for (let i = 0; i < proxiesToEnrich.length; i += batchSize) {
                    const batch = proxiesToEnrich.slice(i, i + batchSize);
                    const geoPromises = batch.map(p => 
                        getGeoInfo(p.ip).then(geoInfo => {
                            p.country = geoInfo.country;
                            p.countryCode = geoInfo.countryCode;
                        })
                    );
                    await Promise.all(geoPromises);
                }
                
                proxyTable.classList.remove('hidden');
                document.getElementById('pagination-controls').classList.remove('hidden');
                applyFiltersAndRender();
            }

            // --- RENDERING & UI ---
            function applyFiltersAndRender() {
                let tempProxies = [...allProxies];
                const countryFilter = countryFilterInput.value.trim().toUpperCase();
                const searchTerm = searchInput.value.trim().toLowerCase();

                if (countryFilter) {
                    const filterCodes = countryFilter.split(',').map(c => c.trim());
                    tempProxies = tempProxies.filter(p => p.countryCode && filterCodes.includes(p.countryCode));
                }
                
                if (searchTerm) {
                    tempProxies = tempProxies.filter(p => 
                        p.ip.toLowerCase().includes(searchTerm) ||
                        p.port.toLowerCase().includes(searchTerm) ||
                        (p.country && p.country.toLowerCase().includes(searchTerm)) ||
                        (p.countryCode && p.countryCode.toLowerCase().includes(searchTerm))
                    );
                }

                filteredProxies = tempProxies;
                sortAndRender();
            }

            function sortAndRender() {
                filteredProxies.sort((a, b) => {
                    const { column, direction } = currentSort;
                    let valA = a[column];
                    let valB = b[column];
                    
                    if (column === 'port') {
                        valA = parseInt(valA, 10); valB = parseInt(valB, 10);
                    } else if (column === 'status') {
                        const order = { 'testing':0, 'good': 1, 'untested': 2, 'failed': 3 };
                        valA = order[a.status] ?? 4;
                        valB = order[b.status] ?? 4;
                        if (valA === 1 && valB === 1) { 
                           valA = a.latency;
                           valB = b.latency;
                        }
                    }

                    if (valA < valB) return direction === 'asc' ? -1 : 1;
                    if (valA > valB) return direction === 'asc' ? 1 : -1;
                    return 0;
                });
                
                updateSortIcons();
                renderTablePage();
            }

            function renderTablePage() {
                proxyList.innerHTML = '';
                proxyCountSpan.textContent = `共找到 ${filteredProxies.length} 个代理`;

                if (filteredProxies.length === 0) {
                    proxyList.innerHTML = `<tr><td colspan="6" class="text-center py-10 text-gray-500 dark:text-gray-400">没有找到符合条件的代理。</td></tr>`;
                    updatePaginationUI(0);
                    return;
                }
                
                const start = (currentPage - 1) * ROWS_PER_PAGE;
                const end = start + ROWS_PER_PAGE;
                const paginatedProxies = filteredProxies.slice(start, end);

                paginatedProxies.forEach(proxy => {
                    const tr = document.createElement('tr');
                    tr.className = 'border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50';
                    tr.setAttribute('data-proxy-id', proxy.id);

                    let statusHtml;
                    switch(proxy.status) {
                        case 'good':
                            statusHtml = `<span class="status-dot bg-green-500"></span><span class="text-green-600 dark:text-green-400">${proxy.latency} ms</span>`;
                            break;
                        case 'failed':
                            statusHtml = `<span class="status-dot bg-red-500"></span><span class="text-red-500 dark:text-red-400">失效</span>`;
                            break;
                        case 'testing':
                            statusHtml = `<span class="status-dot bg-yellow-500 status-testing"></span><span class="text-yellow-500 dark:text-yellow-400">测试中...</span>`;
                            break;
                        default:
                            statusHtml = `<span class="status-dot bg-gray-400"></span><span class="text-gray-500 dark:text-gray-400">未测试</span>`;
                    }

                    tr.innerHTML = `
                        <td class="px-5 py-4 text-sm">${statusHtml}</td>
                        <td class="px-5 py-4 text-sm">${proxy.ip}</td>
                        <td class="px-5 py-4 text-sm">${proxy.port}</td>
                        <td class="px-5 py-4 text-sm">
                            <span class="px-2 py-1 font-semibold leading-tight rounded-full ${
                                proxy.type.includes('HTTP') ? 'bg-green-100 text-green-700 dark:bg-green-700/20 dark:text-green-300' :
                                proxy.type === 'SOCKS4' ? 'bg-blue-100 text-blue-700 dark:bg-blue-700/20 dark:text-blue-300' :
                                'bg-purple-100 text-purple-700 dark:bg-purple-700/20 dark:text-purple-300'
                            }">${proxy.type}</span>
                        </td>
                        <td class="px-5 py-4 text-sm">${proxy.country || '查询中...'} (${proxy.countryCode || ''})</td>
                        <td class="px-5 py-4 text-sm whitespace-nowrap">
                            <button class="test-btn bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 text-xs font-bold py-1 px-2 rounded" data-proxy-id="${proxy.id}">测试</button>
                            <button class="copy-btn bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 text-xs font-bold py-1 px-2 rounded" data-proxy="${proxy.ip}:${proxy.port}">复制</button>
                        </td>
                    `;
                    proxyList.appendChild(tr);
                });
                updatePaginationUI(filteredProxies.length);
            }
            
            function updatePaginationUI(totalRows) {
                const totalPages = Math.ceil(totalRows / ROWS_PER_PAGE);
                pageInfoSpan.textContent = `第 ${currentPage} / ${totalPages || 1} 页`;
                prevPageBtn.disabled = currentPage === 1;
                nextPageBtn.disabled = currentPage === totalPages || totalPages === 0;
                prevPageBtn.classList.toggle('opacity-50', prevPageBtn.disabled);
                nextPageBtn.classList.toggle('opacity-50', nextPageBtn.disabled);
            }

            function updateSortIcons() {
                document.querySelectorAll('#proxy-table th .sort-icon').forEach(icon => icon.textContent = '');
                const currentTh = document.querySelector(`#proxy-table th[data-sort="${currentSort.column}"] .sort-icon`);
                if(currentTh) {
                    currentTh.textContent = currentSort.direction === 'asc' ? '▲' : '▼';
                    currentTh.style.opacity = '1';
                }
            }
            
            function showToast(message, isError = false) {
                toast.textContent = message;
                toast.className = `fixed bottom-10 right-10 text-white py-2 px-4 rounded-lg shadow-xl transition-opacity duration-300 opacity-0 ${isError ? 'bg-red-500' : 'bg-green-500'}`;
                toast.classList.remove('opacity-0');
                setTimeout(() => toast.classList.add('opacity-0'), 2500);
            }

            // --- EVENT HANDLERS & LOGIC ---
            function handleThemeToggle() {
                const isDark = document.documentElement.classList.toggle('dark');
                localStorage.theme = isDark ? 'dark' : 'light';
                updateThemeIcons(isDark);
            }
            
            function updateThemeIcons(isDark) {
                 darkIcon.classList.toggle('hidden', !isDark);
                 lightIcon.classList.toggle('hidden', isDark);
            }
            
            function handleSort(e) {
                const th = e.target.closest('th');
                if (!th || !th.dataset.sort) return;
                const column = th.dataset.sort;
                const direction = (currentSort.column === column && currentSort.direction === 'asc') ? 'desc' : 'asc';
                currentSort = { column, direction };
                sortAndRender();
            }
            
            function handleCopy(e) {
                if (!e.target.classList.contains('copy-btn')) return;
                const proxyAddress = e.target.dataset.proxy;
                const textArea = document.createElement("textarea");
                textArea.value = proxyAddress;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showToast('已成功复制到剪贴板!');
                } catch (err) {
                    console.error('无法复制:', err);
                    showToast('复制失败!', true);
                }
                document.body.removeChild(textArea);
            }

            async function testProxy(proxyId) {
                const proxy = allProxies.find(p => p.id === proxyId);
                if (!proxy) return;

                proxy.status = 'testing';
                renderTablePage(); 

                const startTime = Date.now();
                try {
                    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500));
                    
                    if (Math.random() > 0.4) {
                        proxy.status = 'good';
                        proxy.latency = Date.now() - startTime;
                    } else {
                        throw new Error("Simulated failure");
                    }
                } catch (e) {
                    proxy.status = 'failed';
                    proxy.latency = Infinity;
                }
                
                sortAndRender();
            }

            async function handleTestAll() {
                const start = (currentPage - 1) * ROWS_PER_PAGE;
                const end = start + ROWS_PER_PAGE;
                const visibleProxies = filteredProxies.slice(start, end);

                const testPromises = visibleProxies.map(p => testProxy(p.id));
                await Promise.all(testPromises);
                showToast("当前页代理测试完成!");
            }

            function exportData(format) {
                if (filteredProxies.length === 0) {
                    showToast("没有可导出的代理", true);
                    return;
                }
                
                let content;
                let mimeType;
                let filename;

                const proxiesToExport = filteredProxies.map(p => `${p.ip}:${p.port}`);

                if (format === 'txt') {
                    content = proxiesToExport.join('\n');
                    mimeType = 'text/plain';
                    filename = 'proxies.txt';
                } else { // csv
                    let csvContent = "ip,port,type,country,countryCode,status,latency_ms\n";
                    filteredProxies.forEach(p => {
                        csvContent += `${p.ip},${p.port},${p.type},"${p.country}",${p.countryCode},${p.status},${p.latency === Infinity ? '' : p.latency}\n`;
                    });
                    content = csvContent;
                    mimeType = 'text/csv';
                    filename = 'proxies.csv';
                }

                const blob = new Blob([content], { type: mimeType });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }


            // --- Event Listeners ---
            fetchButton.addEventListener('click', fetchProxies);
            countryFilterInput.addEventListener('input', () => { currentPage = 1; applyFiltersAndRender(); });
            searchInput.addEventListener('input', () => { currentPage = 1; applyFiltersAndRender(); });
            themeToggleBtn.addEventListener('click', handleThemeToggle);
            document.querySelector('#proxy-table thead').addEventListener('click', handleSort);
            proxyList.addEventListener('click', e => {
                handleCopy(e);
                if(e.target.classList.contains('test-btn')) {
                    testProxy(e.target.dataset.proxyId);
                }
            });
            testAllBtn.addEventListener('click', handleTestAll);
            exportTxtBtn.addEventListener('click', () => exportData('txt'));
            exportCsvBtn.addEventListener('click', () => exportData('csv'));
            prevPageBtn.addEventListener('click', () => { if(currentPage > 1) { currentPage--; renderTablePage(); }});
            nextPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(filteredProxies.length / ROWS_PER_PAGE);
                if (currentPage < totalPages) { currentPage++; renderTablePage(); }
            });

            // --- Initial Load ---
            updateThemeIcons(document.documentElement.classList.contains('dark'));
            fetchProxies();
        });
    </script>

</body>
</html>
