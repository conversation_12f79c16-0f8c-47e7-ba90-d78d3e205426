// 代理聚合平台配置文件
const CONFIG = {
    // 应用设置
    app: {
        name: "全功能免费代理聚合平台",
        version: "2.0.0",
        author: "Proxy Aggregator Team",
        rowsPerPage: 50,
        maxConcurrentTests: 10,
        defaultTimeout: 15000
    },

    // 代理数据源配置
    proxySources: {
        // ProxyScrape API - 专业代理服务
        proxyscrape_http: {
            name: "ProxyScrape HTTP",
            url: "https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=http&proxy_format=ipport&format=text",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 1,
            description: "专业代理API服务，实时更新"
        },
        proxyscrape_socks4: {
            name: "ProxyScrape SOCKS4",
            url: "https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=socks4&proxy_format=ipport&format=text",
            type: "socks4",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 1,
            description: "SOCKS4代理列表"
        },
        proxyscrape_socks5: {
            name: "ProxyScrape SOCKS5",
            url: "https://api.proxyscrape.com/v3/free-proxy-list/get?request=displayproxies&protocol=socks5&proxy_format=ipport&format=text",
            type: "socks5",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 1,
            description: "SOCKS5代理列表"
        },

        // Geonode - 高质量代理
        geonode: {
            name: "Geonode",
            url: "https://proxylist.geonode.com/api/proxy-list?limit=500&sort_by=lastChecked&sort_type=desc",
            type: "all",
            parser: "parseGeonodeJsonFormat",
            enabled: true,
            priority: 2,
            description: "高质量代理列表，包含地理位置信息"
        },

        // GitHub开源项目
        github_speedx_http: {
            name: "GitHub SpeedX HTTP",
            url: "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 3,
            description: "GitHub开源HTTP代理列表"
        },
        github_speedx_socks4: {
            name: "GitHub SpeedX SOCKS4",
            url: "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks4.txt",
            type: "socks4",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 3,
            description: "GitHub开源SOCKS4代理列表"
        },
        github_speedx_socks5: {
            name: "GitHub SpeedX SOCKS5",
            url: "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks5.txt",
            type: "socks5",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 3,
            description: "GitHub开源SOCKS5代理列表"
        },

        // Monosans代理列表
        monosans_http: {
            name: "Monosans HTTP",
            url: "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 4,
            description: "Monosans维护的HTTP代理"
        },
        monosans_socks4: {
            name: "Monosans SOCKS4",
            url: "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/socks4.txt",
            type: "socks4",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 4,
            description: "Monosans维护的SOCKS4代理"
        },
        monosans_socks5: {
            name: "Monosans SOCKS5",
            url: "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/socks5.txt",
            type: "socks5",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 4,
            description: "Monosans维护的SOCKS5代理"
        },

        // 其他源
        jetkai_http: {
            name: "Jetkai HTTP",
            url: "https://raw.githubusercontent.com/jetkai/proxy-list/main/online-proxies/txt/proxies-http.txt",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 5,
            description: "Jetkai在线验证的HTTP代理"
        },
        clarketm_proxy: {
            name: "Clarketm Proxy List",
            url: "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
            type: "all",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 6,
            description: "Clarketm维护的综合代理列表"
        },
        sunny9577_http: {
            name: "Sunny9577 HTTP",
            url: "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 7,
            description: "Sunny9577爬取的HTTP代理"
        },
        proxy4parsing: {
            name: "Proxy4Parsing",
            url: "https://raw.githubusercontent.com/proxy4parsing/proxy-list/main/http.txt",
            type: "http",
            parser: "parseIpPortFormat",
            enabled: true,
            priority: 8,
            description: "专用于解析的HTTP代理列表"
        }
    },

    // CORS代理服务配置
    corsProxies: [
        "https://api.codetabs.com/v1/proxy?quest=",
        "https://cors-anywhere.herokuapp.com/",
        "https://thingproxy.freeboard.io/fetch/"
    ],

    // 地理位置API配置
    geoApis: [
        {
            name: "ipapi.co",
            url: "https://ipapi.co/{ip}/json/",
            parser: "parseIpapiCo",
            timeout: 3000
        },
        {
            name: "ipapi.is",
            url: "https://api.ipapi.is?q={ip}",
            parser: "parseIpapiIs",
            timeout: 3000
        },
        {
            name: "ipinfo.io",
            url: "https://ipinfo.io/{ip}/json",
            parser: "parseIpinfo",
            timeout: 3000
        }
    ],

    // 测试配置
    testing: {
        testUrls: [
            "https://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "https://jsonplaceholder.typicode.com/posts/1"
        ],
        timeout: 10000,
        maxRetries: 2,
        successRates: {
            base: 0.3,
            httpBonus: 0.2,
            commonPortBonus: 0.15,
            portRangeBonus: 0.1
        },
        commonPorts: ["80", "8080", "3128", "8888", "1080", "1337", "8000", "9999"]
    },

    // UI配置
    ui: {
        themes: {
            light: {
                primary: "#3B82F6",
                secondary: "#6B7280",
                success: "#10B981",
                warning: "#F59E0B",
                error: "#EF4444"
            },
            dark: {
                primary: "#60A5FA",
                secondary: "#9CA3AF",
                success: "#34D399",
                warning: "#FBBF24",
                error: "#F87171"
            }
        },
        animations: {
            duration: 300,
            easing: "ease-in-out"
        }
    },

    // 导出配置
    export: {
        formats: {
            txt: {
                extension: "txt",
                mimeType: "text/plain",
                template: "{ip}:{port}"
            },
            csv: {
                extension: "csv",
                mimeType: "text/csv",
                headers: ["ip", "port", "type", "country", "countryCode", "status", "latency_ms", "lastTested", "source"]
            },
            json: {
                extension: "json",
                mimeType: "application/json",
                pretty: true
            }
        }
    },

    // 安全配置
    security: {
        warnings: {
            showSecurityWarning: true,
            warningMessage: "免费代理来源不明，可能存在安全和隐私风险。请不要在通过免费代理上网时处理任何敏感信息。",
            acceptedRisks: false
        },
        rateLimit: {
            enabled: true,
            maxRequestsPerMinute: 60,
            maxConcurrentRequests: 10
        }
    },

    // 缓存配置
    cache: {
        enabled: true,
        ttl: 300000, // 5分钟
        maxSize: 1000,
        keys: {
            proxies: "cached_proxies",
            geoInfo: "cached_geo_info",
            testResults: "cached_test_results"
        }
    }
};

// 导出配置（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局配置对象（浏览器环境）
if (typeof window !== 'undefined') {
    window.PROXY_CONFIG = CONFIG;
}
