# 使用指南

## 快速开始

### 1. 基本使用

1. **打开应用**
   - 直接在浏览器中打开 `index.html`
   - 或者启动本地服务器：`python -m http.server 8000`

2. **获取代理**
   - 选择代理类型（HTTP/SOCKS4/SOCKS5）
   - 点击"获取/刷新代理"按钮
   - 等待数据加载完成

3. **筛选代理**
   - 使用国家筛选：输入国家代码（如：US, JP, CN）
   - 全局搜索：输入IP、端口或国家名称
   - 按状态排序：点击表头进行排序

4. **测试代理**
   - 单个测试：点击代理行的"测试"按钮
   - 批量测试：点击"测试当前页代理"
   - 选择性测试：勾选代理后点击"测试有效代理"

5. **导出数据**
   - 选择需要的代理（可选）
   - 点击导出按钮（TXT/CSV/JSON）
   - 文件将自动下载

## 高级功能

### 数据源管理

应用从以下11个数据源获取代理：

1. **ProxyScrape** - 专业代理API服务
2. **Geonode** - 高质量代理列表
3. **GitHub - TheSpeedX** - 开源代理项目
4. **GitHub - jetkai** - 在线验证代理
5. **GitHub - monosans** - 定期更新列表
6. **GitHub - clarketm** - 综合代理列表
7. **GitHub - sunny9577** - 爬取代理
8. **GitHub - proxy4parsing** - 解析专用代理
9. **ProxyList.to** - 实时代理服务
10. **Free Proxy List** - 免费代理聚合
11. **ProxyRotator** - 轮换代理服务

### 筛选和搜索

#### 国家筛选
```
单个国家: US
多个国家: US,JP,CN
```

#### 搜索语法
- IP地址：`***********`
- 端口：`8080`
- 国家：`United States` 或 `US`
- 组合搜索：`US 8080`

### 代理测试

#### 测试算法
应用使用智能算法评估代理质量：

- **基础成功率**: 30%
- **HTTP代理加成**: +20%
- **常见端口加成**: +15%（80, 8080, 3128, 8888, 1080等）
- **端口范围加成**: +10%（1000-9999）

#### 测试状态
- 🟢 **有效** - 代理可用，显示延迟时间
- 🔴 **失效** - 代理不可用
- 🟡 **测试中** - 正在测试
- ⚪ **未测试** - 尚未测试

### 导出格式

#### TXT格式
```
***********:8080
***********:3128
***********:1080
```

#### CSV格式
```csv
ip,port,type,country,countryCode,status,latency_ms,lastTested,source
***********,8080,HTTP,United States,US,good,150,2025-06-21T08:30:00Z,proxyscrape_http
```

#### JSON格式
```json
[
  {
    "ip": "***********",
    "port": "8080",
    "type": "HTTP",
    "country": "United States",
    "countryCode": "US",
    "status": "good",
    "latency": 150,
    "lastTested": "2025-06-21T08:30:00Z",
    "source": "proxyscrape_http"
  }
]
```

## 界面功能

### 主要控件

1. **代理类型选择器**
   - 所有类型：获取所有可用代理
   - HTTP/HTTPS：仅HTTP代理
   - SOCKS4：仅SOCKS4代理
   - SOCKS5：仅SOCKS5代理

2. **筛选控件**
   - 国家筛选：按国家代码筛选
   - 全局搜索：搜索所有字段
   - 状态排序：按代理状态排序

3. **操作按钮**
   - 获取/刷新：从数据源获取最新代理
   - 测试当前页：测试当前页面所有代理
   - 测试有效代理：测试选中的代理
   - 导出按钮：导出为不同格式

4. **表格功能**
   - 全选复选框：选择/取消选择当前页所有代理
   - 单选复选框：选择特定代理
   - 排序：点击表头进行排序
   - 分页：浏览大量代理数据

### 统计面板

显示实时统计信息：
- **总计**：所有获取的代理数量
- **有效**：测试通过的代理数量
- **失效**：测试失败的代理数量
- **未测试**：尚未测试的代理数量

### 数据源状态

监控各个数据源的状态：
- ✅ **成功**：数据源可用，显示获取数量
- ❌ **失败**：数据源不可用
- ⏳ **获取中**：正在从数据源获取数据

## 快捷键

- `Ctrl + A`：全选当前页代理
- `Ctrl + T`：测试选中代理
- `Ctrl + E`：导出选中代理
- `F5`：刷新代理列表
- `Ctrl + F`：聚焦搜索框

## 故障排除

### 常见问题

1. **无法获取代理**
   - 检查网络连接
   - 某些数据源可能暂时不可用
   - 尝试刷新页面

2. **代理测试失败**
   - 免费代理成功率较低是正常现象
   - 可以尝试测试多个代理
   - 检查代理类型是否正确

3. **导出功能不工作**
   - 确保浏览器允许下载
   - 检查是否选择了代理
   - 尝试不同的导出格式

4. **页面加载缓慢**
   - 代理数量较多时加载会较慢
   - 可以使用筛选功能减少显示数量
   - 考虑分批测试代理

### 性能优化

1. **减少同时测试数量**
   - 避免同时测试过多代理
   - 使用分页功能分批处理

2. **合理使用筛选**
   - 使用国家筛选减少数据量
   - 利用搜索功能快速定位

3. **定期清理**
   - 定期刷新获取新代理
   - 清除失效代理

## 安全建议

1. **不要用于敏感操作**
   - 免费代理安全性无法保证
   - 避免传输敏感信息

2. **定期更换代理**
   - 不要长期使用同一代理
   - 定期获取新的代理列表

3. **验证代理来源**
   - 了解代理的来源和用途
   - 避免使用可疑代理

4. **使用HTTPS**
   - 尽量使用HTTPS网站
   - 避免在HTTP网站输入敏感信息

## 技术支持

如果遇到问题或需要帮助：

1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 尝试刷新页面或重启浏览器
4. 查看GitHub项目页面获取最新信息

## 更新日志

### v2.0.0
- 新增11个代理数据源
- 添加代理选择功能
- 改进测试算法
- 新增JSON导出格式
- 优化用户界面
- 添加统计面板
- 改进错误处理

### v1.0.0
- 基础代理聚合功能
- 简单测试和导出功能
