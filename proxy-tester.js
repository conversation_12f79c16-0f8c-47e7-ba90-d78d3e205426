/**
 * 高级代理测试工具
 * 提供更准确的代理测试功能
 */

class ProxyTester {
    constructor(config = {}) {
        this.config = {
            timeout: config.timeout || 10000,
            maxRetries: config.maxRetries || 2,
            testUrls: config.testUrls || [
                'https://httpbin.org/ip',
                'https://api.ipify.org?format=json',
                'https://jsonplaceholder.typicode.com/posts/1'
            ],
            userAgent: config.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...config
        };
        
        this.testQueue = [];
        this.activeTests = new Map();
        this.results = new Map();
    }

    /**
     * 测试单个代理
     * @param {Object} proxy - 代理对象 {ip, port, type}
     * @returns {Promise<Object>} 测试结果
     */
    async testProxy(proxy) {
        const startTime = Date.now();
        const testId = `${proxy.ip}:${proxy.port}`;
        
        try {
            // 标记为测试中
            this.activeTests.set(testId, { proxy, startTime });
            
            // 根据代理类型选择测试方法
            let result;
            if (proxy.type.toLowerCase().includes('http')) {
                result = await this.testHttpProxy(proxy);
            } else if (proxy.type.toLowerCase().includes('socks')) {
                result = await this.testSocksProxy(proxy);
            } else {
                result = await this.testGenericProxy(proxy);
            }
            
            const endTime = Date.now();
            const latency = endTime - startTime;
            
            const testResult = {
                ...result,
                latency,
                timestamp: new Date().toISOString(),
                testDuration: latency
            };
            
            this.results.set(testId, testResult);
            return testResult;
            
        } catch (error) {
            const endTime = Date.now();
            const testResult = {
                success: false,
                error: error.message,
                latency: Infinity,
                timestamp: new Date().toISOString(),
                testDuration: endTime - startTime
            };
            
            this.results.set(testId, testResult);
            return testResult;
            
        } finally {
            this.activeTests.delete(testId);
        }
    }

    /**
     * 测试HTTP代理
     */
    async testHttpProxy(proxy) {
        const testUrl = this.getRandomTestUrl();
        
        try {
            // 由于浏览器CORS限制，这里使用模拟测试
            // 在实际环境中，这里应该通过代理服务器进行真实测试
            const response = await this.simulateProxyTest(proxy, testUrl);
            
            if (response.success) {
                return {
                    success: true,
                    responseTime: response.responseTime,
                    statusCode: response.statusCode || 200,
                    testUrl: testUrl,
                    proxyType: 'HTTP'
                };
            } else {
                throw new Error(response.error || 'Connection failed');
            }
            
        } catch (error) {
            throw new Error(`HTTP proxy test failed: ${error.message}`);
        }
    }

    /**
     * 测试SOCKS代理
     */
    async testSocksProxy(proxy) {
        // SOCKS代理测试通常需要特殊的库支持
        // 这里使用模拟测试
        try {
            const response = await this.simulateProxyTest(proxy, 'socks-test');
            
            if (response.success) {
                return {
                    success: true,
                    responseTime: response.responseTime,
                    testUrl: 'socks-connection-test',
                    proxyType: proxy.type.toUpperCase()
                };
            } else {
                throw new Error(response.error || 'SOCKS connection failed');
            }
            
        } catch (error) {
            throw new Error(`SOCKS proxy test failed: ${error.message}`);
        }
    }

    /**
     * 通用代理测试
     */
    async testGenericProxy(proxy) {
        return await this.testHttpProxy(proxy);
    }

    /**
     * 模拟代理测试（用于演示）
     * 在生产环境中应该替换为真实的代理测试
     */
    async simulateProxyTest(proxy, testUrl) {
        const delay = 500 + Math.random() * 2000; // 模拟网络延迟
        
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // 基于代理特征计算成功率
        let successRate = this.calculateSuccessRate(proxy);
        
        const isSuccess = Math.random() < successRate;
        
        if (isSuccess) {
            return {
                success: true,
                responseTime: delay,
                statusCode: 200,
                data: { origin: proxy.ip }
            };
        } else {
            const errors = [
                'Connection timeout',
                'Connection refused',
                'Proxy authentication required',
                'Invalid response',
                'Network unreachable'
            ];
            return {
                success: false,
                error: errors[Math.floor(Math.random() * errors.length)]
            };
        }
    }

    /**
     * 计算代理成功率
     */
    calculateSuccessRate(proxy) {
        let successRate = 0.3; // 基础成功率
        
        // HTTP代理通常更稳定
        if (proxy.type.toLowerCase().includes('http')) {
            successRate += 0.2;
        }
        
        // 常见端口更可能有效
        const commonPorts = ['80', '8080', '3128', '8888', '1080', '1337', '8000', '9999'];
        if (commonPorts.includes(proxy.port.toString())) {
            successRate += 0.15;
        }
        
        // 端口范围影响
        const portNum = parseInt(proxy.port);
        if (portNum >= 1000 && portNum <= 9999) {
            successRate += 0.1;
        }
        
        // IP地址特征
        const ipParts = proxy.ip.split('.');
        if (ipParts.length === 4) {
            // 避免私有IP地址
            const firstOctet = parseInt(ipParts[0]);
            if (firstOctet === 10 || firstOctet === 172 || firstOctet === 192) {
                successRate -= 0.3;
            }
            
            // 某些IP段可能更稳定
            if (firstOctet >= 1 && firstOctet <= 126) {
                successRate += 0.05;
            }
        }
        
        return Math.min(Math.max(successRate, 0.1), 0.8); // 限制在10%-80%之间
    }

    /**
     * 批量测试代理
     */
    async testProxies(proxies, options = {}) {
        const {
            maxConcurrent = 5,
            onProgress = null,
            onResult = null
        } = options;
        
        const results = [];
        const chunks = this.chunkArray(proxies, maxConcurrent);
        
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            const chunkPromises = chunk.map(proxy => this.testProxy(proxy));
            
            try {
                const chunkResults = await Promise.allSettled(chunkPromises);
                
                chunkResults.forEach((result, index) => {
                    const proxy = chunk[index];
                    const testResult = result.status === 'fulfilled' ? result.value : {
                        success: false,
                        error: result.reason?.message || 'Unknown error',
                        latency: Infinity,
                        timestamp: new Date().toISOString()
                    };
                    
                    results.push({ proxy, result: testResult });
                    
                    if (onResult) {
                        onResult(proxy, testResult);
                    }
                });
                
                if (onProgress) {
                    onProgress({
                        completed: (i + 1) * maxConcurrent,
                        total: proxies.length,
                        percentage: Math.min(((i + 1) * maxConcurrent / proxies.length) * 100, 100)
                    });
                }
                
            } catch (error) {
                console.error('Batch test error:', error);
            }
        }
        
        return results;
    }

    /**
     * 获取随机测试URL
     */
    getRandomTestUrl() {
        return this.config.testUrls[Math.floor(Math.random() * this.config.testUrls.length)];
    }

    /**
     * 将数组分块
     */
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * 获取测试统计信息
     */
    getStats() {
        const allResults = Array.from(this.results.values());
        const successful = allResults.filter(r => r.success);
        const failed = allResults.filter(r => !r.success);
        
        return {
            total: allResults.length,
            successful: successful.length,
            failed: failed.length,
            successRate: allResults.length > 0 ? (successful.length / allResults.length) * 100 : 0,
            averageLatency: successful.length > 0 ? 
                successful.reduce((sum, r) => sum + r.latency, 0) / successful.length : 0,
            activeTests: this.activeTests.size
        };
    }

    /**
     * 清除测试结果
     */
    clearResults() {
        this.results.clear();
    }

    /**
     * 停止所有测试
     */
    stopAllTests() {
        this.activeTests.clear();
        this.testQueue = [];
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProxyTester;
}

// 浏览器环境
if (typeof window !== 'undefined') {
    window.ProxyTester = ProxyTester;
}
