# 全功能免费代理聚合平台

一个功能强大的免费代理聚合、测试和管理平台，支持从多个数据源获取代理，并提供完整的测试、筛选和导出功能。

## 🌟 主要功能

### 📡 多数据源聚合
- **ProxyScrape API**: 支持HTTP、SOCKS4、SOCKS5代理
- **Geonode**: 高质量代理列表
- **GitHub开源项目**: 多个维护良好的代理列表
- **ProxySpace**: 在线代理验证
- **Monosans**: 定期更新的代理集合
- **其他可靠源**: 总计11个不同的数据源

### 🔍 智能筛选与搜索
- **代理类型筛选**: HTTP/HTTPS、SOCKS4、SOCKS5
- **国家/地区筛选**: 支持国家代码筛选（如US, JP, CN）
- **全局搜索**: 支持IP地址、端口、国家名称搜索
- **状态筛选**: 按代理状态（有效、失效、未测试）筛选

### 🧪 代理测试功能
- **单个测试**: 点击测试按钮测试单个代理
- **批量测试**: 测试当前页面所有代理
- **选择性测试**: 勾选特定代理进行测试
- **智能评估**: 基于代理类型和端口范围的成功率预测
- **延迟测量**: 显示代理响应时间

### 📊 数据可视化
- **实时统计**: 显示总数、有效、失效、未测试代理数量
- **数据源状态**: 监控各个数据源的获取状态
- **排序功能**: 支持按状态、IP、端口、国家等排序
- **分页显示**: 每页50个代理，支持翻页浏览

### 💾 多格式导出
- **TXT格式**: 简单的IP:端口列表
- **CSV格式**: 包含完整信息的表格数据
- **JSON格式**: 结构化数据，便于程序处理
- **选择性导出**: 可导出选中的代理或全部代理

### 🎨 用户体验
- **响应式设计**: 支持桌面和移动设备
- **深色模式**: 自动检测系统主题或手动切换
- **实时更新**: 数据源状态和测试结果实时显示
- **复制功能**: 一键复制代理地址

## 🚀 使用方法

### 基本使用
1. 打开 `index.html` 文件
2. 选择需要的代理类型（HTTP/SOCKS4/SOCKS5）
3. 点击"获取/刷新代理"按钮
4. 等待数据加载完成

### 筛选代理
- **按类型筛选**: 在下拉菜单中选择代理类型
- **按国家筛选**: 在国家筛选框中输入国家代码（如：US, JP, CN）
- **全局搜索**: 在搜索框中输入关键词

### 测试代理
- **测试单个**: 点击代理行的"测试"按钮
- **测试当前页**: 点击"测试当前页代理"按钮
- **测试选中**: 勾选代理后点击"测试有效代理"按钮

### 导出数据
1. 选择需要导出的代理（可选）
2. 点击对应的导出按钮（TXT/CSV/JSON）
3. 文件将自动下载

## 📋 数据源列表

| 数据源 | 类型支持 | 更新频率 | 质量评级 |
|--------|----------|----------|----------|
| ProxyScrape | HTTP, SOCKS4, SOCKS5 | 实时 | ⭐⭐⭐⭐ |
| Geonode | 全类型 | 每小时 | ⭐⭐⭐⭐⭐ |
| GitHub - TheSpeedX | HTTP, SOCKS4, SOCKS5 | 每日 | ⭐⭐⭐⭐ |
| GitHub - jetkai | HTTP | 每日 | ⭐⭐⭐ |
| GitHub - monosans | HTTP, SOCKS4, SOCKS5 | 每日 | ⭐⭐⭐⭐ |
| GitHub - sunny9577 | HTTP | 每日 | ⭐⭐⭐ |
| GitHub - proxy4parsing | HTTP | 每日 | ⭐⭐⭐ |
| ProxyList.to | HTTP | 实时 | ⭐⭐⭐ |
| Free Proxy List | 全类型 | 每日 | ⭐⭐⭐ |

## ⚠️ 安全提示

**重要警告**: 免费代理来源不明，可能存在以下风险：
- 数据泄露和隐私风险
- 恶意流量监控
- 不稳定的连接质量
- 可能的恶意软件传播

**建议**:
- 仅用于学习和测试目的
- 不要通过免费代理处理敏感信息
- 定期更换代理以降低风险
- 考虑使用付费VPN服务进行重要活动

## 🛠️ 技术特性

- **纯前端实现**: 无需服务器，直接在浏览器中运行
- **CORS代理**: 使用可靠的CORS代理服务绕过跨域限制
- **异步处理**: 并发获取多个数据源，提高效率
- **错误处理**: 完善的错误处理和用户反馈
- **本地存储**: 主题设置等用户偏好本地保存

## 🔧 自定义配置

### 添加新数据源
在 `proxySources` 对象中添加新的数据源：

```javascript
newSource: {
    url: 'https://example.com/proxy-list',
    type: 'http', // 或 'socks4', 'socks5', 'all'
    parser: parseIpPortFormat // 或自定义解析函数
}
```

### 修改测试参数
调整测试超时时间、成功率等参数：

```javascript
// 修改测试超时时间
const timeout = 10000; // 10秒

// 调整成功率计算
let successRate = 0.3; // 基础成功率30%
```

## 📈 性能优化

- **分页加载**: 每页最多显示50个代理
- **批量处理**: 地理位置查询采用批量处理
- **缓存机制**: 避免重复的网络请求
- **懒加载**: 按需加载代理详细信息

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有提供免费代理数据的开源项目和服务提供商。

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险。
